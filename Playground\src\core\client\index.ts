// Client-side Core framework exports
export { <PERSON><PERSON><PERSON><PERSON>, ClientCore_Instance, initializeClientCore, onServerEvent, fireServer } from "./ClientCore";
export { 
    ClientState, 
    useClientState, 
    useUIState,
    type ClientGameState,
    type ClientAbilitySlot,
    type VoiceChatParticipant,
    type PlayerAbility,
    type ClientStateAction,
    type ClientStateUpdater
} from "./ClientStateManager";
