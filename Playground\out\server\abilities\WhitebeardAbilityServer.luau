-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
-- Whitebeard visual synchronization server using new Core Framework
-- This syncs visual effects across all clients without adding damage
local _core = TS.import(script, game:GetService("ReplicatedStorage"), "core")
local Core = _core.Core
local Result = _core.Result
local createError = _core.createError
local WhitebeardAbilityServer
do
	WhitebeardAbilityServer = setmetatable({}, {
		__tostring = function()
			return "WhitebeardAbilityServer"
		end,
	})
	WhitebeardAbilityServer.__index = WhitebeardAbilityServer
	function WhitebeardAbilityServer.new(...)
		local self = setmetatable({}, WhitebeardAbilityServer)
		return self:constructor(...) or self
	end
	function WhitebeardAbilityServer:constructor()
		self:setupVisualSync()
		print("🥊 Whitebeard visual sync server initialized")
	end
	WhitebeardAbilityServer.setupVisualSync = TS.async(function(self)
		local networkService = Core.getNetworkService()
		if networkService:isError() then
			warn(`Failed to get network service: {networkService:getError().message}`)
			return nil
		end
		local validationService = Core.getValidationService()
		if validationService:isError() then
			warn(`Failed to get validation service: {validationService:getError().message}`)
			return nil
		end
		-- Register Whitebeard visual sync event
		local registerResult = networkService:getValue():registerEvent(Core.eventName("WhitebeardVisualSync"), TS.async(function(player, request)
			return TS.await(self:handleVisualSync(player, request))
		end), {
			validateRequest = function(request)
				local _request = request
				if typeof(_request) ~= "table" then
					return Result:err(createError("Request must be a table"))
				end
				local req = request
				local _punchType = req.punchType
				local _condition = typeof(_punchType) ~= "string"
				if not _condition then
					_condition = (req.punchType ~= "single" and req.punchType ~= "double")
				end
				if _condition then
					return Result:err(createError("punchType must be 'single' or 'double'"))
				end
				local _casterUserId = req.casterUserId
				if typeof(_casterUserId) ~= "number" then
					return Result:err(createError("casterUserId must be a number"))
				end
				local _position = req.position
				if typeof(_position) ~= "Vector3" then
					return Result:err(createError("position must be a Vector3"))
				end
				local _timestamp = req.timestamp
				if typeof(_timestamp) ~= "number" then
					return Result:err(createError("timestamp must be a number"))
				end
				return Result:ok(request)
			end,
			rateLimit = {
				maxCalls = 1,
				windowMs = 12000,
			},
			requiresAuth = false,
		})
		if registerResult:isError() then
			warn(`Failed to register WhitebeardVisualSync event: {registerResult:getError().message}`)
		else
			print("✅ Whitebeard visual synchronization setup complete")
		end
	end)
	WhitebeardAbilityServer.handleVisualSync = TS.async(function(self, player, request)
		print(`🥊 {player.Name} requesting {request.punchType} punch visual sync`)
		local networkService = Core.getNetworkService()
		if networkService:isError() then
			return Result:err(createError(`Network service unavailable: {networkService:getError().message}`))
		end
		local validationService = Core.getValidationService()
		if validationService:isError() then
			return Result:err(createError(`Validation service unavailable: {validationService:getError().message}`))
		end
		-- Validate player position
		local positionResult = validationService:getValue():validatePlayerPosition(player, request.position, 100)
		if positionResult:isError() then
			return Result:err(createError(`Invalid position: {positionResult:getError().message}`))
		end
		-- Create effect data for replication
		local effectData = {
			type = "whitebeard_quake",
			casterUserId = request.casterUserId,
			punchType = request.punchType,
			position = request.position,
			timestamp = request.timestamp,
		}
		-- Broadcast to all clients (excluding the originator)
		local broadcastResult = networkService:getValue():fireAllClients(Core.eventName("WhitebeardEffectReplicate"), effectData, Core.playerId(player.UserId))
		if broadcastResult:isError() then
			return Result:err(createError(`Failed to broadcast effect: {broadcastResult:getError().message}`))
		end
		print(`✅ Visual effects replicated to all clients for {player.Name}'s {request.punchType} punch`)
		return Result:ok(true)
	end)
end
return {
	WhitebeardAbilityServer = WhitebeardAbilityServer,
}
