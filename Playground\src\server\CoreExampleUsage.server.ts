import { HttpService } from "@rbxts/services";
import {
    initializeCoreFramework,
    Core,
    Result,
    StateManager,
    StateAction,
    createError
} from "../core";

// Example game state interface
interface GameState {
    players: Map<number, PlayerData>;
    worldSettings: {
        gravity: number;
        timeOfDay: number;
        weather: string;
    };
    gameMode: "lobby" | "playing" | "ended";
}

interface PlayerData {
    id: number;
    name: string;
    level: number;
    experience: number;
    position: Vector3;
}

// Example of modern Core framework usage
async function initializeGameServer() {
    print("🚀 Initializing game server with Core Framework...");

    // Initialize the Core Framework
    const initResult = await initializeCoreFramework();
    if (initResult.isError()) {
        error(`Failed to initialize Core Framework: ${initResult.getError().message}`);
        return;
    }

    // Create game state manager
    const initialState: GameState = {
        players: new Map(),
        worldSettings: {
            gravity: 196.2,
            timeOfDay: 12,
            weather: "sunny"
        },
        gameMode: "lobby"
    };

    const stateManager = Core.createStateManager(initialState, "GameStateManager");
    const stateInitResult = await stateManager.initialize();
    if (stateInitResult.isError()) {
        error(`Failed to initialize state manager: ${stateInitResult.getError().message}`);
        return;
    }

    // Subscribe to state changes
    const subscribeResult = stateManager.subscribe(
        "game-mode-watcher",
        (prevState, newState) => {
            if (prevState.gameMode !== newState.gameMode) {
                print(`🎮 Game mode changed: ${prevState.gameMode} → ${newState.gameMode}`);
            }
        },
        (state) => state.gameMode // Only notify when game mode changes
    );

    if (subscribeResult.isError()) {
        warn(`Failed to subscribe to state changes: ${subscribeResult.getError().message}`);
    }

    // Set up networking
    const networkService = Core.getNetworkService();
    if (networkService.isOk()) {
        // Register a player join event
        const registerResult = networkService.getValue().registerEvent(
            Core.eventName("PlayerJoinGame"),
            async (player: Player, request: { characterName: string }) => {
                print(`📥 Player ${player.Name} wants to join with character: ${request.characterName}`);

                // Add player to state
                const addPlayerAction: StateAction<GameState, PlayerData> = {
                    type: "ADD_PLAYER",
                    payload: {
                        id: player.UserId,
                        name: request.characterName,
                        level: 1,
                        experience: 0,
                        position: new Vector3(0, 10, 0)
                    },
                    reducer: (state, playerData) => {
                        const newState = { ...state };
                        newState.players = new Map([...state.players]);
                        newState.players.set(playerData.id, playerData);
                        return newState;
                    },
                    undoReducer: (state) => {
                        const newState = { ...state };
                        newState.players = new Map([...state.players]);
                        newState.players.delete(player.UserId);
                        return newState;
                    }
                };

                const dispatchResult = stateManager.dispatch(addPlayerAction);
                if (dispatchResult.isError()) {
                    return Result.err(createError(`Failed to add player to state: ${dispatchResult.getError().message}`));
                }

                return Result.ok({ success: true, playerId: Core.playerId(player.UserId) });
            },
            {
                validateRequest: (request: unknown) => {
                    if (typeOf(request) !== "table") {
                        return Result.err(createError("Request must be a table"));
                    }
                    const req = request as Record<string, unknown>;
                    if (typeOf(req.characterName) !== "string") {
                        return Result.err(createError("characterName must be a string"));
                    }
                    return Result.ok(request as { characterName: string });
                },
                rateLimit: { maxCalls: 1, windowMs: 5000 }, // Once per 5 seconds
                requiresAuth: false
            }
        );

        if (registerResult.isError()) {
            warn(`Failed to register PlayerJoinGame event: ${registerResult.getError().message}`);
        } else {
            print("✅ PlayerJoinGame event registered successfully");
        }

        // Register a change weather event
        const weatherResult = networkService.getValue().registerEvent(
            Core.eventName("ChangeWeather"),
            async (player: Player, request: { weather: string }) => {
                print(`🌤️ Player ${player.Name} wants to change weather to: ${request.weather}`);

                const changeWeatherAction: StateAction<GameState, string> = {
                    type: "CHANGE_WEATHER",
                    payload: request.weather,
                    reducer: (state, weather) => ({
                        ...state,
                        worldSettings: {
                            ...state.worldSettings,
                            weather
                        }
                    }),
                    validator: (state) => {
                        const validWeathers = ["sunny", "rainy", "stormy", "foggy"];
                        if (!validWeathers.includes(state.worldSettings.weather)) {
                            return Result.err(createError("Invalid weather type"));
                        }
                        return Result.ok(undefined);
                    }
                };

                const dispatchResult = stateManager.dispatch(changeWeatherAction);
                if (dispatchResult.isError()) {
                    return Result.err(createError(`Failed to change weather: ${dispatchResult.getError().message}`));
                }

                // Notify all clients about weather change
                const broadcastResult = networkService.getValue().fireAllClients(
                    Core.eventName("WeatherChanged"),
                    { weather: request.weather }
                );

                if (broadcastResult.isError()) {
                    warn(`Failed to broadcast weather change: ${broadcastResult.getError().message}`);
                }

                return Result.ok({ success: true, newWeather: request.weather });
            },
            {
                validateRequest: (request: unknown) => {
                    if (typeOf(request) !== "table") {
                        return Result.err(createError("Request must be a table"));
                    }
                    const req = request as Record<string, unknown>;
                    if (typeOf(req.weather) !== "string") {
                        return Result.err(createError("weather must be a string"));
                    }
                    return Result.ok(request as { weather: string });
                },
                rateLimit: { maxCalls: 3, windowMs: 10000 }, // 3 times per 10 seconds
                requiresAuth: true
            }
        );

        if (weatherResult.isError()) {
            warn(`Failed to register ChangeWeather event: ${weatherResult.getError().message}`);
        } else {
            print("✅ ChangeWeather event registered successfully");
        }
    } else {
        warn(`Failed to get network service: ${networkService.getError().message}`);
    }

    // Example of state manipulation
    task.wait(2);
    print("🎮 Starting game...");
    
    const startGameAction: StateAction<GameState, void> = {
        type: "START_GAME",
        payload: undefined,
        reducer: (state) => ({
            ...state,
            gameMode: "playing"
        }),
        undoReducer: (state) => ({
            ...state,
            gameMode: "lobby"
        })
    };

    const startResult = stateManager.dispatch(startGameAction);
    if (startResult.isError()) {
        warn(`Failed to start game: ${startResult.getError().message}`);
    } else {
        print("✅ Game started successfully!");
        print(`Current state: ${HttpService.JSONEncode(startResult.getValue())}`);
    }

    print("🎉 Game server initialization complete!");
}

// Initialize the server
initializeGameServer().catch((err) => {
    warn(`Server initialization failed: ${err}`);
});
