import { ReplicatedStorage, Players } from "@rbxts/services";
import { BaseService } from "../foundation/BaseService";
import { Result } from "../foundation/types/Result";
import { EventName, PlayerId } from "../foundation/types/BrandedTypes";
import { Error, createError } from "../foundation/types/RobloxError";
import { NetworkError } from "./errors/NetworkError";
import { RemoteEventDescriptor } from "./interfaces/RemoteEventDescriptor";
import { NetworkValidationService } from "./NetworkValidationService";

export class NetworkService extends BaseService {
    private remoteEvents = new Map<string, RemoteEvent>();
    private eventDescriptors = new Map<string, RemoteEventDescriptor<any, any>>();
    private validationService?: NetworkValidationService;

    constructor() {
        super("NetworkService");
    }

    protected async onInitialize(): Promise<Result<void, Error>> {
        this.createRemoteEventsFolder();
        return Result.ok(undefined);
    }

    protected async onShutdown(): Promise<Result<void, Error>> {
        this.remoteEvents.clear();
        this.eventDescriptors.clear();
        return Result.ok(undefined);
    }

    public setValidationService(validationService: NetworkValidationService): void {
        this.validationService = validationService;
    }

    public registerEvent<TRequest, TResponse>(
        eventName: EventName,
        handler: (player: Player, request: TRequest) => Promise<Result<TResponse, Error>>,
        options: {
            validateRequest?: (request: unknown) => Result<TRequest, Error>;
            rateLimit?: { maxCalls: number; windowMs: number };
            requiresAuth?: boolean;
        } = {}
    ): Result<void, NetworkError> {
        const initResult = this.ensureInitialized();
        if (initResult.isError()) {
            return Result.err(new NetworkError(initResult.getError().message));
        }

        if (this.eventDescriptors.has(eventName)) {
            return Result.err(new NetworkError(`Event '${eventName}' is already registered`));
        }

        const remoteEvent = this.createRemoteEvent(eventName);
        if (remoteEvent.isError()) {
            return Result.err(new NetworkError(remoteEvent.getError().message));
        }

        const descriptor: RemoteEventDescriptor<TRequest, TResponse> = {
            eventName,
            handler,
            options,
            remoteEvent: remoteEvent.getValue()
        };

        this.eventDescriptors.set(eventName, descriptor);
        this.setupEventHandler(descriptor);

        this.logInfo(`Registered network event: ${eventName}`);
        return Result.ok(undefined);
    }

    public fireClient<T>(
        eventName: EventName,
        playerId: PlayerId,
        data: T
    ): Result<void, NetworkError> {
        const remoteEvent = this.remoteEvents.get(eventName);
        if (!remoteEvent) {
            return Result.err(new NetworkError(`Event '${eventName}' is not registered`));
        }

        const player = Players.GetPlayerByUserId(playerId);
        if (!player) {
            return Result.err(new NetworkError(`Player with ID ${playerId} not found`));
        }

        try {
            remoteEvent.FireClient(player, data);
            return Result.ok(undefined);
        } catch (error) {
            return Result.err(new NetworkError(`Failed to fire client event: ${error}`));
        }
    }

    public fireAllClients<T>(
        eventName: EventName,
        data: T,
        excludePlayer?: PlayerId
    ): Result<void, NetworkError> {
        const remoteEvent = this.remoteEvents.get(eventName);
        if (!remoteEvent) {
            return Result.err(new NetworkError(`Event '${eventName}' is not registered`));
        }

        try {
            if (excludePlayer) {
                const excludedPlayer = Players.GetPlayerByUserId(excludePlayer);
                for (const player of Players.GetPlayers()) {
                    if (player !== excludedPlayer) {
                        remoteEvent.FireClient(player, data);
                    }
                }
            } else {
                remoteEvent.FireAllClients(data);
            }
            return Result.ok(undefined);
        } catch (error) {
            return Result.err(new NetworkError(`Failed to fire all clients event: ${error}`));
        }
    }

    private createRemoteEventsFolder(): void {
        let folder = ReplicatedStorage.FindFirstChild("RemoteEvents") as Folder;
        if (!folder) {
            folder = new Instance("Folder");
            folder.Name = "RemoteEvents";
            folder.Parent = ReplicatedStorage;
        }
    }

    private createRemoteEvent(eventName: EventName): Result<RemoteEvent, Error> {
        try {
            const remoteEvent = new Instance("RemoteEvent");
            remoteEvent.Name = eventName;
            
            const folder = ReplicatedStorage.FindFirstChild("RemoteEvents") as Folder;
            remoteEvent.Parent = folder;

            this.remoteEvents.set(eventName, remoteEvent);
            return Result.ok(remoteEvent);
        } catch (error) {
            return Result.err(createError(`Failed to create RemoteEvent: ${error}`));
        }
    }

    private setupEventHandler<TRequest, TResponse>(
        descriptor: RemoteEventDescriptor<TRequest, TResponse>
    ): void {
        descriptor.remoteEvent.OnServerEvent.Connect(async (player: Player, ...args: unknown[]) => {
            let requestData = args[0];

            // Validate request if validator provided
            if (descriptor.options.validateRequest) {
                const validationResult = descriptor.options.validateRequest(requestData);
                if (validationResult.isError()) {
                    this.sendErrorResponse(descriptor.remoteEvent, player, validationResult.getError().message);
                    return;
                }
                requestData = validationResult.getValue();
            }

            // Rate limiting check
            if (this.validationService && descriptor.options.rateLimit) {
                const rateLimitResult = this.validationService.checkRateLimit(
                    player,
                    descriptor.eventName,
                    descriptor.options.rateLimit
                );
                if (rateLimitResult.isError()) {
                    this.sendErrorResponse(descriptor.remoteEvent, player, "Rate limit exceeded");
                    return;
                }
            }

            // Authentication check
            if (descriptor.options.requiresAuth && this.validationService) {
                const authResult = this.validationService.validateAuthentication(player);
                if (authResult.isError()) {
                    this.sendErrorResponse(descriptor.remoteEvent, player, "Authentication required");
                    return;
                }
            }

            // Execute handler
            try {
                const result = await descriptor.handler(player, requestData as TRequest);
                if (result.isOk()) {
                    descriptor.remoteEvent.FireClient(player, {
                        success: true,
                        data: result.getValue(),
                        timestamp: tick()
                    });
                } else {
                    this.sendErrorResponse(descriptor.remoteEvent, player, result.getError().message);
                }
            } catch (error) {
                this.logError(`Handler error for event ${descriptor.eventName}: ${error}`);
                this.sendErrorResponse(descriptor.remoteEvent, player, "Internal server error");
            }
        });
    }

    private sendErrorResponse(remoteEvent: RemoteEvent, player: Player, errorMessage: string): void {
        remoteEvent.FireClient(player, {
            success: false,
            error: errorMessage,
            timestamp: tick()
        });
    }
}
