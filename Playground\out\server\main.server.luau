-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitFor<PERSON>hild("RuntimeLib"))
local initializeCoreFramework = TS.import(script, game:GetService("ReplicatedStorage"), "core").initializeCoreFramework
local WhitebeardAbilityServer = TS.import(script, game:GetService("ServerScriptService"), "TS", "abilities", "WhitebeardAbilityServer").WhitebeardAbilityServer
local WorldTestingServer = TS.import(script, game:GetService("ServerScriptService"), "TS", "world", "WorldTestingServer").WorldTestingServer
local ServerDataStoreService = TS.import(script, game:GetService("ServerScriptService"), "TS", "data", "DataStoreService").ServerDataStoreService
-- Initialize the new enterprise-grade Core Framework
local initializeServer = TS.async(function()
	print("🚀 Initializing server with enterprise Core Framework...")
	local initResult = TS.await(initializeCoreFramework())
	if initResult:isError() then
		error(`Failed to initialize Core Framework: {initResult:getError().message}`)
		return nil
	end
	print("✅ Core Framework initialized successfully!")
	-- Initialize game-specific systems
	local whitebeardServer = WhitebeardAbilityServer.new()
	local worldTestingServer = WorldTestingServer.new()
	local dataStoreService = ServerDataStoreService:getInstance()
	print("🎮 Game server ready!")
end)
-- Start server initialization
initializeServer():catch(function(err)
	warn(`Server initialization failed: {err}`)
end)
print("🔥 Playground server loaded with Core helpers, Gravity testing, and DataStore service!")
