// Whitebeard visual synchronization server using new Core Framework
// This syncs visual effects across all clients without adding damage

import { Core, Result, createError, Error } from "../../core";

interface VisualSyncRequest {
    punchType: "single" | "double";
    casterUserId: number;
    position: Vector3;
    timestamp: number;
}

interface WhitebeardEffectData {
    type: "whitebeard_quake";
    casterUserId: number;
    punchType: "single" | "double";
    position: Vector3;
    timestamp: number;
}

export class WhitebeardAbilityServer {
    constructor() {
        this.setupVisualSync();
        print("🥊 Whitebeard visual sync server initialized");
    }

    private async setupVisualSync(): Promise<void> {
        const networkService = Core.getNetworkService();
        if (networkService.isError()) {
            warn(`Failed to get network service: ${networkService.getError().message}`);
            return;
        }

        const validationService = Core.getValidationService();
        if (validationService.isError()) {
            warn(`Failed to get validation service: ${validationService.getError().message}`);
            return;
        }

        // Register Whitebeard visual sync event
        const registerResult = networkService.getValue().registerEvent(
            Core.eventName("WhitebeardVisualSync"),
            async (player: Player, request: VisualSyncRequest) => {
                return await this.handleVisualSync(player, request);
            },
            {
                validateRequest: (request: unknown) => {
                    if (typeOf(request) !== "table") {
                        return Result.err(createError("Request must be a table"));
                    }
                    const req = request as Record<string, unknown>;

                    if (typeOf(req.punchType) !== "string" || (req.punchType !== "single" && req.punchType !== "double")) {
                        return Result.err(createError("punchType must be 'single' or 'double'"));
                    }
                    if (typeOf(req.casterUserId) !== "number") {
                        return Result.err(createError("casterUserId must be a number"));
                    }
                    if (typeOf(req.position) !== "Vector3") {
                        return Result.err(createError("position must be a Vector3"));
                    }
                    if (typeOf(req.timestamp) !== "number") {
                        return Result.err(createError("timestamp must be a number"));
                    }

                    return Result.ok(request as VisualSyncRequest);
                },
                rateLimit: { maxCalls: 1, windowMs: 12000 }, // Once per 12 seconds
                requiresAuth: false
            }
        );

        if (registerResult.isError()) {
            warn(`Failed to register WhitebeardVisualSync event: ${registerResult.getError().message}`);
        } else {
            print("✅ Whitebeard visual synchronization setup complete");
        }
    }

    private async handleVisualSync(player: Player, request: VisualSyncRequest): Promise<Result<boolean, Error>> {
        print(`🥊 ${player.Name} requesting ${request.punchType} punch visual sync`);

        const networkService = Core.getNetworkService();
        if (networkService.isError()) {
            return Result.err(createError(`Network service unavailable: ${networkService.getError().message}`));
        }

        const validationService = Core.getValidationService();
        if (validationService.isError()) {
            return Result.err(createError(`Validation service unavailable: ${validationService.getError().message}`));
        }

        // Validate player position
        const positionResult = validationService.getValue().validatePlayerPosition(
            player,
            request.position,
            100
        );
        if (positionResult.isError()) {
            return Result.err(createError(`Invalid position: ${positionResult.getError().message}`));
        }

        // Create effect data for replication
        const effectData: WhitebeardEffectData = {
            type: "whitebeard_quake",
            casterUserId: request.casterUserId,
            punchType: request.punchType,
            position: request.position,
            timestamp: request.timestamp
        };

        // Broadcast to all clients (excluding the originator)
        const broadcastResult = networkService.getValue().fireAllClients(
            Core.eventName("WhitebeardEffectReplicate"),
            effectData,
            Core.playerId(player.UserId)
        );

        if (broadcastResult.isError()) {
            return Result.err(createError(`Failed to broadcast effect: ${broadcastResult.getError().message}`));
        }

        print(`✅ Visual effects replicated to all clients for ${player.Name}'s ${request.punchType} punch`);
        return Result.ok(true);
    }

}
