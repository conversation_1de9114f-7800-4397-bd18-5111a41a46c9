-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local HttpService = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services").HttpService
local _core = TS.import(script, game:GetService("ReplicatedStorage"), "core")
local initializeCoreFramework = _core.initializeCoreFramework
local Core = _core.Core
local Result = _core.Result
local createError = _core.createError
-- Example game state interface
-- Example of modern Core framework usage
local initializeGameServer = TS.async(function()
	print("🚀 Initializing game server with Core Framework...")
	-- Initialize the Core Framework
	local initResult = TS.await(initializeCoreFramework())
	if initResult:isError() then
		error(`Failed to initialize Core Framework: {initResult:getError().message}`)
		return nil
	end
	-- Create game state manager
	local initialState = {
		players = {},
		worldSettings = {
			gravity = 196.2,
			timeOfDay = 12,
			weather = "sunny",
		},
		gameMode = "lobby",
	}
	local stateManager = Core.createStateManager(initialState, "GameStateManager")
	local stateInitResult = TS.await(stateManager:initialize())
	if stateInitResult:isError() then
		error(`Failed to initialize state manager: {stateInitResult:getError().message}`)
		return nil
	end
	-- Subscribe to state changes
	local subscribeResult = stateManager:subscribe("game-mode-watcher", function(prevState, newState)
		if prevState.gameMode ~= newState.gameMode then
			print(`🎮 Game mode changed: {prevState.gameMode} → {newState.gameMode}`)
		end
	end, function(state)
		return state.gameMode
	end)
	if subscribeResult:isError() then
		warn(`Failed to subscribe to state changes: {subscribeResult:getError().message}`)
	end
	-- Set up networking
	local networkService = Core.getNetworkService()
	if networkService:isOk() then
		-- Register a player join event
		local registerResult = networkService:getValue():registerEvent(Core.eventName("PlayerJoinGame"), TS.async(function(player, request)
			print(`📥 Player {player.Name} wants to join with character: {request.characterName}`)
			-- Add player to state
			local addPlayerAction = {
				type = "ADD_PLAYER",
				payload = {
					id = player.UserId,
					name = request.characterName,
					level = 1,
					experience = 0,
					position = Vector3.new(0, 10, 0),
				},
				reducer = function(state, playerData)
					local _object = table.clone(state)
					setmetatable(_object, nil)
					local newState = _object
					local _array = {}
					local _length = #_array
					for _k, _v in state.players do
						_length += 1
						_array[_length] = { _k, _v }
					end
					local _map = {}
					for _, _v in _array do
						_map[_v[1]] = _v[2]
					end
					newState.players = _map
					local _players = newState.players
					local _id = playerData.id
					local _playerData = playerData
					_players[_id] = _playerData
					return newState
				end,
				undoReducer = function(state)
					local _object = table.clone(state)
					setmetatable(_object, nil)
					local newState = _object
					local _array = {}
					local _length = #_array
					for _k, _v in state.players do
						_length += 1
						_array[_length] = { _k, _v }
					end
					local _map = {}
					for _, _v in _array do
						_map[_v[1]] = _v[2]
					end
					newState.players = _map
					local _players = newState.players
					local _userId = player.UserId
					_players[_userId] = nil
					return newState
				end,
			}
			local dispatchResult = stateManager:dispatch(addPlayerAction)
			if dispatchResult:isError() then
				return Result:err(createError(`Failed to add player to state: {dispatchResult:getError().message}`))
			end
			return Result:ok({
				success = true,
				playerId = Core.playerId(player.UserId),
			})
		end), {
			validateRequest = function(request)
				local _request = request
				if typeof(_request) ~= "table" then
					return Result:err(createError("Request must be a table"))
				end
				local req = request
				local _characterName = req.characterName
				if typeof(_characterName) ~= "string" then
					return Result:err(createError("characterName must be a string"))
				end
				return Result:ok(request)
			end,
			rateLimit = {
				maxCalls = 1,
				windowMs = 5000,
			},
			requiresAuth = false,
		})
		if registerResult:isError() then
			warn(`Failed to register PlayerJoinGame event: {registerResult:getError().message}`)
		else
			print("✅ PlayerJoinGame event registered successfully")
		end
		-- Register a change weather event
		local weatherResult = networkService:getValue():registerEvent(Core.eventName("ChangeWeather"), TS.async(function(player, request)
			print(`🌤️ Player {player.Name} wants to change weather to: {request.weather}`)
			local changeWeatherAction = {
				type = "CHANGE_WEATHER",
				payload = request.weather,
				reducer = function(state, weather)
					local _object = table.clone(state)
					setmetatable(_object, nil)
					local _left = "worldSettings"
					local _object_1 = table.clone(state.worldSettings)
					setmetatable(_object_1, nil)
					_object_1.weather = weather
					_object[_left] = _object_1
					return _object
				end,
				validator = function(state)
					local validWeathers = { "sunny", "rainy", "stormy", "foggy" }
					local _weather = state.worldSettings.weather
					if not (table.find(validWeathers, _weather) ~= nil) then
						return Result:err(createError("Invalid weather type"))
					end
					return Result:ok(nil)
				end,
			}
			local dispatchResult = stateManager:dispatch(changeWeatherAction)
			if dispatchResult:isError() then
				return Result:err(createError(`Failed to change weather: {dispatchResult:getError().message}`))
			end
			-- Notify all clients about weather change
			local broadcastResult = networkService:getValue():fireAllClients(Core.eventName("WeatherChanged"), {
				weather = request.weather,
			})
			if broadcastResult:isError() then
				warn(`Failed to broadcast weather change: {broadcastResult:getError().message}`)
			end
			return Result:ok({
				success = true,
				newWeather = request.weather,
			})
		end), {
			validateRequest = function(request)
				local _request = request
				if typeof(_request) ~= "table" then
					return Result:err(createError("Request must be a table"))
				end
				local req = request
				local _weather = req.weather
				if typeof(_weather) ~= "string" then
					return Result:err(createError("weather must be a string"))
				end
				return Result:ok(request)
			end,
			rateLimit = {
				maxCalls = 3,
				windowMs = 10000,
			},
			requiresAuth = true,
		})
		if weatherResult:isError() then
			warn(`Failed to register ChangeWeather event: {weatherResult:getError().message}`)
		else
			print("✅ ChangeWeather event registered successfully")
		end
	else
		warn(`Failed to get network service: {networkService:getError().message}`)
	end
	-- Example of state manipulation
	task.wait(2)
	print("🎮 Starting game...")
	local startGameAction = {
		type = "START_GAME",
		payload = nil,
		reducer = function(state)
			local _object = table.clone(state)
			setmetatable(_object, nil)
			_object.gameMode = "playing"
			return _object
		end,
		undoReducer = function(state)
			local _object = table.clone(state)
			setmetatable(_object, nil)
			_object.gameMode = "lobby"
			return _object
		end,
	}
	local startResult = stateManager:dispatch(startGameAction)
	if startResult:isError() then
		warn(`Failed to start game: {startResult:getError().message}`)
	else
		print("✅ Game started successfully!")
		print(`Current state: {HttpService:JSONEncode(startResult:getValue())}`)
	end
	print("🎉 Game server initialization complete!")
end)
-- Initialize the server
initializeGameServer():catch(function(err)
	warn(`Server initialization failed: {err}`)
end)
