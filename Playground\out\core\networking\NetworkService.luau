-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local ReplicatedStorage = _services.ReplicatedStorage
local Players = _services.Players
local BaseService = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "BaseService").BaseService
local Result = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "Result").Result
local createError = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "RobloxError").createError
local NetworkError = TS.import(script, game:GetService("ReplicatedStorage"), "core", "networking", "errors", "NetworkError").NetworkError
local NetworkService
do
	local super = BaseService
	NetworkService = setmetatable({}, {
		__tostring = function()
			return "NetworkService"
		end,
		__index = super,
	})
	NetworkService.__index = NetworkService
	function NetworkService.new(...)
		local self = setmetatable({}, NetworkService)
		return self:constructor(...) or self
	end
	function NetworkService:constructor()
		super.constructor(self, "NetworkService")
		self.remoteEvents = {}
		self.eventDescriptors = {}
	end
	NetworkService.onInitialize = TS.async(function(self)
		self:createRemoteEventsFolder()
		return Result:ok(nil)
	end)
	NetworkService.onShutdown = TS.async(function(self)
		table.clear(self.remoteEvents)
		table.clear(self.eventDescriptors)
		return Result:ok(nil)
	end)
	function NetworkService:setValidationService(validationService)
		self.validationService = validationService
	end
	function NetworkService:registerEvent(eventName, handler, options)
		if options == nil then
			options = {}
		end
		local initResult = self:ensureInitialized()
		if initResult:isError() then
			return Result:err(NetworkError.new(initResult:getError().message))
		end
		local _eventDescriptors = self.eventDescriptors
		local _eventName = eventName
		if _eventDescriptors[_eventName] ~= nil then
			return Result:err(NetworkError.new(`Event '{eventName}' is already registered`))
		end
		local remoteEvent = self:createRemoteEvent(eventName)
		if remoteEvent:isError() then
			return Result:err(NetworkError.new(remoteEvent:getError().message))
		end
		local descriptor = {
			eventName = eventName,
			handler = handler,
			options = options,
			remoteEvent = remoteEvent:getValue(),
		}
		local _eventDescriptors_1 = self.eventDescriptors
		local _eventName_1 = eventName
		_eventDescriptors_1[_eventName_1] = descriptor
		self:setupEventHandler(descriptor)
		self:logInfo(`Registered network event: {eventName}`)
		return Result:ok(nil)
	end
	function NetworkService:fireClient(eventName, playerId, data)
		local _remoteEvents = self.remoteEvents
		local _eventName = eventName
		local remoteEvent = _remoteEvents[_eventName]
		if not remoteEvent then
			return Result:err(NetworkError.new(`Event '{eventName}' is not registered`))
		end
		local player = Players:GetPlayerByUserId(playerId)
		if not player then
			return Result:err(NetworkError.new(`Player with ID {playerId} not found`))
		end
		local _exitType, _returns = TS.try(function()
			remoteEvent:FireClient(player, data)
			return TS.TRY_RETURN, { Result:ok(nil) }
		end, function(error)
			return TS.TRY_RETURN, { Result:err(NetworkError.new(`Failed to fire client event: {error}`)) }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end
	function NetworkService:fireAllClients(eventName, data, excludePlayer)
		local _remoteEvents = self.remoteEvents
		local _eventName = eventName
		local remoteEvent = _remoteEvents[_eventName]
		if not remoteEvent then
			return Result:err(NetworkError.new(`Event '{eventName}' is not registered`))
		end
		local _exitType, _returns = TS.try(function()
			if excludePlayer ~= 0 and excludePlayer == excludePlayer and excludePlayer then
				local excludedPlayer = Players:GetPlayerByUserId(excludePlayer)
				for _, player in Players:GetPlayers() do
					if player ~= excludedPlayer then
						remoteEvent:FireClient(player, data)
					end
				end
			else
				remoteEvent:FireAllClients(data)
			end
			return TS.TRY_RETURN, { Result:ok(nil) }
		end, function(error)
			return TS.TRY_RETURN, { Result:err(NetworkError.new(`Failed to fire all clients event: {error}`)) }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end
	function NetworkService:createRemoteEventsFolder()
		local folder = ReplicatedStorage:FindFirstChild("RemoteEvents")
		if not folder then
			folder = Instance.new("Folder")
			folder.Name = "RemoteEvents"
			folder.Parent = ReplicatedStorage
		end
	end
	function NetworkService:createRemoteEvent(eventName)
		local _exitType, _returns = TS.try(function()
			local remoteEvent = Instance.new("RemoteEvent")
			remoteEvent.Name = eventName
			local folder = ReplicatedStorage:FindFirstChild("RemoteEvents")
			remoteEvent.Parent = folder
			local _remoteEvents = self.remoteEvents
			local _eventName = eventName
			_remoteEvents[_eventName] = remoteEvent
			return TS.TRY_RETURN, { Result:ok(remoteEvent) }
		end, function(error)
			return TS.TRY_RETURN, { Result:err(createError(`Failed to create RemoteEvent: {error}`)) }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end
	function NetworkService:setupEventHandler(descriptor)
		descriptor.remoteEvent.OnServerEvent:Connect(TS.async(function(player, ...)
			local args = { ... }
			local requestData = args[1]
			-- Validate request if validator provided
			if descriptor.options.validateRequest then
				local validationResult = descriptor.options.validateRequest(requestData)
				if validationResult:isError() then
					self:sendErrorResponse(descriptor.remoteEvent, player, validationResult:getError().message)
					return nil
				end
				requestData = validationResult:getValue()
			end
			-- Rate limiting check
			if self.validationService and descriptor.options.rateLimit then
				local rateLimitResult = self.validationService:checkRateLimit(player, descriptor.eventName, descriptor.options.rateLimit)
				if rateLimitResult:isError() then
					self:sendErrorResponse(descriptor.remoteEvent, player, "Rate limit exceeded")
					return nil
				end
			end
			-- Authentication check
			if descriptor.options.requiresAuth and self.validationService then
				local authResult = self.validationService:validateAuthentication(player)
				if authResult:isError() then
					self:sendErrorResponse(descriptor.remoteEvent, player, "Authentication required")
					return nil
				end
			end
			-- Execute handler
			TS.try(function()
				local result = TS.await(descriptor.handler(player, requestData))
				if result:isOk() then
					descriptor.remoteEvent:FireClient(player, {
						success = true,
						data = result:getValue(),
						timestamp = tick(),
					})
				else
					self:sendErrorResponse(descriptor.remoteEvent, player, result:getError().message)
				end
			end, function(error)
				self:logError(`Handler error for event {descriptor.eventName}: {error}`)
				self:sendErrorResponse(descriptor.remoteEvent, player, "Internal server error")
			end)
		end))
	end
	function NetworkService:sendErrorResponse(remoteEvent, player, errorMessage)
		remoteEvent:FireClient(player, {
			success = false,
			error = errorMessage,
			timestamp = tick(),
		})
	end
end
return {
	NetworkService = NetworkService,
}
