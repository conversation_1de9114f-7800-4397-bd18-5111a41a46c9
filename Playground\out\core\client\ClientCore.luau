-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local ReplicatedStorage = _services.ReplicatedStorage
local RunService = _services.RunService
local Result = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "Result").Result
local createError = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "RobloxError").createError
local ClientCore
do
	ClientCore = setmetatable({}, {
		__tostring = function()
			return "ClientCore"
		end,
	})
	ClientCore.__index = ClientCore
	function ClientCore.new(...)
		local self = setmetatable({}, ClientCore)
		return self:constructor(...) or self
	end
	function ClientCore:constructor()
		self.eventHandlers = {}
		self.remoteEvents = {}
		self.isInitialized = false
	end
	function ClientCore:getInstance()
		if not ClientCore.instance then
			ClientCore.instance = ClientCore.new()
		end
		return ClientCore.instance
	end
	ClientCore.initialize = TS.async(function(self)
		if self.isInitialized then
			return Result:ok(nil)
		end
		local _exitType, _returns = TS.try(function()
			-- Wait for server to create RemoteEvents
			TS.await(self:waitForServerEvents())
			self.isInitialized = true
			print("🚀 Client Core initialized successfully!")
			return TS.TRY_RETURN, { Result:ok(nil) }
		end, function(error)
			return TS.TRY_RETURN, { Result:err(createError(`Failed to initialize Client Core: {error}`)) }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end)
	function ClientCore:onServerEvent(eventName, handler)
		if not self.isInitialized then
			return Result:err(createError("Client Core is not initialized"))
		end
		local _remoteEvents = self.remoteEvents
		local _eventName = eventName
		local remoteEvent = _remoteEvents[_eventName]
		if not remoteEvent then
			return Result:err(createError(`RemoteEvent '{eventName}' not found`))
		end
		local clientHandler = {
			eventName = eventName,
			handler = function(data)
				return handler(data)
			end,
		}
		local _eventHandlers = self.eventHandlers
		local _eventName_1 = eventName
		_eventHandlers[_eventName_1] = clientHandler
		-- Connect to server event
		remoteEvent.OnClientEvent:Connect(function(data)
			if data.success and data.data ~= nil then
				handler(data.data)
			else
				local _value = data.error
				if _value ~= "" and _value then
					warn(`Server event error for '{eventName}': {data.error}`)
				end
			end
		end)
		return Result:ok(nil)
	end
	function ClientCore:fireServer(eventName, data)
		if not self.isInitialized then
			return Result:err(createError("Client Core is not initialized"))
		end
		local _remoteEvents = self.remoteEvents
		local _eventName = eventName
		local remoteEvent = _remoteEvents[_eventName]
		if not remoteEvent then
			return Result:err(createError(`RemoteEvent '{eventName}' not found`))
		end
		local _exitType, _returns = TS.try(function()
			remoteEvent:FireServer(data)
			return TS.TRY_RETURN, { Result:ok(nil) }
		end, function(error)
			return TS.TRY_RETURN, { Result:err(createError(`Failed to fire server event: {error}`)) }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end
	ClientCore.waitForServerEvents = TS.async(function(self)
		local maxWaitTime = 30
		local startTime = tick()
		while tick() - startTime < maxWaitTime do
			local remoteEventsFolder = ReplicatedStorage:FindFirstChild("RemoteEvents")
			if remoteEventsFolder then
				-- Cache all RemoteEvents
				for _, child in remoteEventsFolder:GetChildren() do
					if child:IsA("RemoteEvent") then
						local _remoteEvents = self.remoteEvents
						local _name = child.Name
						_remoteEvents[_name] = child
					end
				end
				return nil
			end
			-- Wait a frame before checking again
			RunService.Heartbeat:Wait()
		end
		error(`Timeout waiting for server RemoteEvents`)
	end)
	function ClientCore:getEventNames()
		local names = {}
		for name in self.remoteEvents do
			table.insert(names, name)
		end
		return names
	end
	function ClientCore:isEventAvailable(eventName)
		local _remoteEvents = self.remoteEvents
		local _eventName = eventName
		return _remoteEvents[_eventName] ~= nil
	end
end
-- Global client core instance
local ClientCore_Instance = ClientCore:getInstance()
-- Convenience functions
local function initializeClientCore()
	return ClientCore_Instance:initialize()
end
local function onServerEvent(eventName, handler)
	return ClientCore_Instance:onServerEvent(eventName, handler)
end
local function fireServer(eventName, data)
	return ClientCore_Instance:fireServer(eventName, data)
end
return {
	initializeClientCore = initializeClientCore,
	onServerEvent = onServerEvent,
	fireServer = fireServer,
	ClientCore = ClientCore,
	ClientCore_Instance = ClientCore_Instance,
}
