import { make<PERSON><PERSON> } from "../shared/module";
import { initializeCoreFramework, Core } from "../core";
import { WhitebeardAbilityServer } from "./abilities/WhitebeardAbilityServer";
import { WorldTestingServer } from "./world/WorldTestingServer";
import { ServerDataStoreService } from "./data/DataStoreService";

// Initialize the new enterprise-grade Core Framework
async function initializeServer() {
    print("🚀 Initializing server with enterprise Core Framework...");

    const initResult = await initializeCoreFramework();
    if (initResult.isError()) {
        error(`Failed to initialize Core Framework: ${initResult.getError().message}`);
        return;
    }

    print("✅ Core Framework initialized successfully!");

    // Initialize game-specific systems
    const whitebeardServer = new WhitebeardAbilityServer();
    const worldTestingServer = new WorldTestingServer();
    const dataStoreService = ServerDataStoreService.getInstance();

    print("🎮 Game server ready!");
}

// Start server initialization
initializeServer().catch((err) => {
    warn(`Server initialization failed: ${err}`);
});

print("🔥 Playground server loaded with Core helpers, Gravity testing, and DataStore service!");

